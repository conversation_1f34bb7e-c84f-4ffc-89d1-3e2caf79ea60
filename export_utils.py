import csv
import json
import xml.etree.ElementTree as ET
from io import StringIO, BytesIO
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib import colors
from datetime import datetime

def export_to_json(weather_requests):
    """Export weather requests to JSON format"""
    try:
        data = {
            'export_date': datetime.now().isoformat(),
            'total_records': len(weather_requests),
            'weather_requests': [req.to_dict() for req in weather_requests]
        }
        return json.dumps(data, indent=2, default=str)
    except Exception as e:
        raise Exception(f"Error exporting to JSON: {str(e)}")

def export_to_csv(weather_requests):
    """Export weather requests to CSV format"""
    try:
        output = StringIO()
        writer = csv.writer(output)
        
        # Write header
        headers = [
            'ID', 'Location', 'Normalized Location', 'Latitude', 'Longitude',
            'Start Date', 'End Date', 'Request Date', 'User Notes', 'Weather Data Summary'
        ]
        writer.writerow(headers)
        
        # Write data rows
        for req in weather_requests:
            weather_data = req.get_weather_data()
            weather_summary = ""
            if weather_data:
                # Create a summary of weather data
                current_items = [item for item in weather_data if item.get('type') == 'current']
                if current_items:
                    current = current_items[0]
                    weather_summary = f"Temp: {current.get('temperature', 'N/A')}°C, {current.get('description', 'N/A')}"
            
            row = [
                req.id,
                req.location,
                req.normalized_location,
                req.latitude,
                req.longitude,
                req.start_date.isoformat() if req.start_date else '',
                req.end_date.isoformat() if req.end_date else '',
                req.request_date.isoformat() if req.request_date else '',
                req.user_notes or '',
                weather_summary
            ]
            writer.writerow(row)
        
        return output.getvalue()
    except Exception as e:
        raise Exception(f"Error exporting to CSV: {str(e)}")

def export_to_xml(weather_requests):
    """Export weather requests to XML format"""
    try:
        root = ET.Element("weather_export")
        root.set("export_date", datetime.now().isoformat())
        root.set("total_records", str(len(weather_requests)))
        
        requests_elem = ET.SubElement(root, "weather_requests")
        
        for req in weather_requests:
            req_elem = ET.SubElement(requests_elem, "weather_request")
            req_elem.set("id", str(req.id))
            
            # Basic info
            ET.SubElement(req_elem, "location").text = req.location
            ET.SubElement(req_elem, "normalized_location").text = req.normalized_location
            ET.SubElement(req_elem, "latitude").text = str(req.latitude) if req.latitude else ""
            ET.SubElement(req_elem, "longitude").text = str(req.longitude) if req.longitude else ""
            ET.SubElement(req_elem, "start_date").text = req.start_date.isoformat() if req.start_date else ""
            ET.SubElement(req_elem, "end_date").text = req.end_date.isoformat() if req.end_date else ""
            ET.SubElement(req_elem, "request_date").text = req.request_date.isoformat() if req.request_date else ""
            ET.SubElement(req_elem, "user_notes").text = req.user_notes or ""
            
            # Weather data
            weather_data = req.get_weather_data()
            if weather_data:
                weather_elem = ET.SubElement(req_elem, "weather_data")
                for item in weather_data:
                    item_elem = ET.SubElement(weather_elem, "weather_item")
                    item_elem.set("type", item.get('type', ''))
                    item_elem.set("date", item.get('date', ''))
                    
                    ET.SubElement(item_elem, "temperature").text = str(item.get('temperature', ''))
                    ET.SubElement(item_elem, "description").text = item.get('description', '')
                    ET.SubElement(item_elem, "humidity").text = str(item.get('humidity', ''))
                    ET.SubElement(item_elem, "pressure").text = str(item.get('pressure', ''))
        
        return ET.tostring(root, encoding='unicode')
    except Exception as e:
        raise Exception(f"Error exporting to XML: {str(e)}")

def export_to_pdf(weather_requests):
    """Export weather requests to PDF format"""
    try:
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=letter)
        styles = getSampleStyleSheet()
        story = []
        
        # Title
        title = Paragraph("Weather Requests Export", styles['Title'])
        story.append(title)
        story.append(Spacer(1, 12))
        
        # Export info
        export_info = Paragraph(f"Export Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}<br/>Total Records: {len(weather_requests)}", styles['Normal'])
        story.append(export_info)
        story.append(Spacer(1, 12))
        
        # Create table data
        table_data = [
            ['ID', 'Location', 'Start Date', 'End Date', 'Notes']
        ]
        
        for req in weather_requests:
            row = [
                str(req.id),
                req.normalized_location[:30] + '...' if len(req.normalized_location) > 30 else req.normalized_location,
                req.start_date.strftime('%Y-%m-%d') if req.start_date else '',
                req.end_date.strftime('%Y-%m-%d') if req.end_date else '',
                (req.user_notes[:20] + '...') if req.user_notes and len(req.user_notes) > 20 else (req.user_notes or '')
            ]
            table_data.append(row)
        
        # Create table
        table = Table(table_data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
        
        # Add weather data details for each request
        for req in weather_requests:
            story.append(Spacer(1, 12))
            story.append(Paragraph(f"Weather Data for {req.normalized_location}", styles['Heading2']))
            
            weather_data = req.get_weather_data()
            if weather_data:
                for item in weather_data[:3]:  # Limit to first 3 items
                    weather_text = f"Date: {item.get('date', 'N/A')}, Type: {item.get('type', 'N/A')}, Temp: {item.get('temperature', 'N/A')}°C, {item.get('description', 'N/A')}"
                    story.append(Paragraph(weather_text, styles['Normal']))
            else:
                story.append(Paragraph("No weather data available", styles['Normal']))
        
        doc.build(story)
        buffer.seek(0)
        return buffer.getvalue()
    except Exception as e:
        raise Exception(f"Error exporting to PDF: {str(e)}")

def export_to_markdown(weather_requests):
    """Export weather requests to Markdown format"""
    try:
        md_content = []
        
        # Header
        md_content.append("# Weather Requests Export")
        md_content.append("")
        md_content.append(f"**Export Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        md_content.append(f"**Total Records:** {len(weather_requests)}")
        md_content.append("")
        
        # Table header
        md_content.append("| ID | Location | Start Date | End Date | Notes |")
        md_content.append("|---|---|---|---|---|")
        
        # Table rows
        for req in weather_requests:
            notes = req.user_notes.replace('|', '\\|') if req.user_notes else ''
            if len(notes) > 30:
                notes = notes[:30] + '...'
            
            row = f"| {req.id} | {req.normalized_location} | {req.start_date.strftime('%Y-%m-%d') if req.start_date else ''} | {req.end_date.strftime('%Y-%m-%d') if req.end_date else ''} | {notes} |"
            md_content.append(row)
        
        md_content.append("")
        md_content.append("## Weather Data Details")
        md_content.append("")
        
        # Detailed weather data
        for req in weather_requests:
            md_content.append(f"### {req.normalized_location} (ID: {req.id})")
            md_content.append("")
            
            weather_data = req.get_weather_data()
            if weather_data:
                for item in weather_data[:5]:  # Limit to first 5 items
                    md_content.append(f"- **{item.get('date', 'N/A')}** ({item.get('type', 'N/A')}): {item.get('temperature', 'N/A')}°C, {item.get('description', 'N/A')}")
            else:
                md_content.append("- No weather data available")
            
            md_content.append("")
        
        return '\n'.join(md_content)
    except Exception as e:
        raise Exception(f"Error exporting to Markdown: {str(e)}")
