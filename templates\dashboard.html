<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Weather Dashboard</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .tabs {
            display: flex;
            background: white;
            border-radius: 10px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            background: #f8f9fa;
            border: none;
            transition: all 0.3s;
        }
        .tab.active {
            background: #4a90e2;
            color: white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #e0e0e0;
            border-radius: 5px;
            font-size: 14px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s;
        }
        .btn-primary { background: #4a90e2; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn:hover { transform: translateY(-2px); }
        .weather-request-item {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .export-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            width: 80%;
            max-width: 600px;
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover { color: black; }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-cloud-sun"></i> Advanced Weather Dashboard</h1>
            <p>Complete weather management with CRUD operations and data export</p>
        </header>

        <!-- Tab Navigation -->
        <div class="tabs">
            <button class="tab active" onclick="showTab('current-weather')">
                <i class="fas fa-thermometer-half"></i> Current Weather
            </button>
            <button class="tab" onclick="showTab('weather-requests')">
                <i class="fas fa-database"></i> Weather Requests
            </button>
            <button class="tab" onclick="showTab('create-request')">
                <i class="fas fa-plus"></i> Create Request
            </button>
            <button class="tab" onclick="showTab('export-data')">
                <i class="fas fa-download"></i> Export Data
            </button>
        </div>

        <!-- Current Weather Tab -->
        <div id="current-weather" class="tab-content active">
            <div class="search-section">
                <div class="search-container">
                    <input type="text" id="locationInput" placeholder="Enter city, zip code, or coordinates (lat,lon)">
                    <button id="searchBtn"><i class="fas fa-search"></i></button>
                    <button id="locationBtn" title="Use current location"><i class="fas fa-location-arrow"></i></button>
                </div>
                <div class="search-examples">
                    <p>Try: "New York", "10001", "40.7128,-74.0060", or "London, UK"</p>
                </div>
            </div>

            <div id="loading" class="loading hidden">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Getting weather data...</p>
            </div>

            <div id="error" class="error hidden">
                <i class="fas fa-exclamation-triangle"></i>
                <p id="errorMessage"></p>
            </div>

            <div id="weatherContainer" class="weather-container hidden">
                <!-- Current Weather Display (same as original) -->
                <div class="current-weather">
                    <div class="weather-header">
                        <h2 id="currentLocation"></h2>
                        <div class="weather-main">
                            <img id="currentIcon" src="" alt="Weather icon">
                            <div class="temperature">
                                <span id="currentTemp"></span>°C
                            </div>
                        </div>
                        <p id="currentDescription"></p>
                    </div>
                    
                    <div class="weather-details">
                        <div class="detail-item">
                            <i class="fas fa-thermometer-half"></i>
                            <span>Feels like</span>
                            <span id="feelsLike"></span>°C
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-tint"></i>
                            <span>Humidity</span>
                            <span id="humidity"></span>%
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-wind"></i>
                            <span>Wind</span>
                            <span id="windSpeed"></span> m/s
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-eye"></i>
                            <span>Visibility</span>
                            <span id="visibility"></span> km
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-compress-arrows-alt"></i>
                            <span>Pressure</span>
                            <span id="pressure"></span> hPa
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-sun"></i>
                            <span>Sunrise</span>
                            <span id="sunrise"></span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-moon"></i>
                            <span>Sunset</span>
                            <span id="sunset"></span>
                        </div>
                    </div>
                </div>

                <!-- 5-Day Forecast -->
                <div class="forecast-section">
                    <h3><i class="fas fa-calendar-alt"></i> 5-Day Forecast</h3>
                    <div id="forecastContainer" class="forecast-container">
                        <!-- Forecast items will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Weather Requests Tab -->
        <div id="weather-requests" class="tab-content">
            <h2>Saved Weather Requests</h2>
            <div id="weatherRequestsList">
                <!-- Weather requests will be loaded here -->
            </div>
            <div id="requestsPagination" class="pagination">
                <!-- Pagination will be loaded here -->
            </div>
        </div>

        <!-- Create Request Tab -->
        <div id="create-request" class="tab-content">
            <h2>Create New Weather Request</h2>
            <form id="createRequestForm">
                <div class="form-group">
                    <label for="requestLocation">Location:</label>
                    <input type="text" id="requestLocation" placeholder="Enter city, zip code, or coordinates" required>
                </div>
                <div class="form-group">
                    <label for="startDate">Start Date:</label>
                    <input type="date" id="startDate" required>
                </div>
                <div class="form-group">
                    <label for="endDate">End Date:</label>
                    <input type="date" id="endDate" required>
                </div>
                <div class="form-group">
                    <label for="userNotes">Notes (optional):</label>
                    <textarea id="userNotes" rows="3" placeholder="Add any notes about this weather request"></textarea>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Create Weather Request
                </button>
            </form>
        </div>

        <!-- Export Data Tab -->
        <div id="export-data" class="tab-content">
            <h2>Export Weather Data</h2>
            <p>Download your weather requests in various formats:</p>
            <div class="export-buttons">
                <button class="btn btn-info" onclick="exportData('json')">
                    <i class="fas fa-file-code"></i> Export as JSON
                </button>
                <button class="btn btn-info" onclick="exportData('csv')">
                    <i class="fas fa-file-csv"></i> Export as CSV
                </button>
                <button class="btn btn-info" onclick="exportData('xml')">
                    <i class="fas fa-file-code"></i> Export as XML
                </button>
                <button class="btn btn-info" onclick="exportData('pdf')">
                    <i class="fas fa-file-pdf"></i> Export as PDF
                </button>
                <button class="btn btn-info" onclick="exportData('markdown')">
                    <i class="fas fa-file-alt"></i> Export as Markdown
                </button>
            </div>
        </div>
    </div>

    <!-- Edit Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeEditModal()">&times;</span>
            <h2>Edit Weather Request</h2>
            <form id="editRequestForm">
                <input type="hidden" id="editRequestId">
                <div class="form-group">
                    <label for="editLocation">Location:</label>
                    <input type="text" id="editLocation" required>
                </div>
                <div class="form-group">
                    <label for="editStartDate">Start Date:</label>
                    <input type="date" id="editStartDate" required>
                </div>
                <div class="form-group">
                    <label for="editEndDate">End Date:</label>
                    <input type="date" id="editEndDate" required>
                </div>
                <div class="form-group">
                    <label for="editUserNotes">Notes:</label>
                    <textarea id="editUserNotes" rows="3"></textarea>
                </div>
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-save"></i> Update Request
                </button>
            </form>
        </div>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
    <script src="{{ url_for('static', filename='dashboard.js') }}"></script>
</body>
</html>
