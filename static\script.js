document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    var locationInput = document.getElementById('locationInput');
    var searchBtn = document.getElementById('searchBtn');
    var locationBtn = document.getElementById('locationBtn');
    var loading = document.getElementById('loading');
    var error = document.getElementById('error');
    var weatherContainer = document.getElementById('weatherContainer');
    var errorMessage = document.getElementById('errorMessage');

    // Set up event listeners
    searchBtn.addEventListener('click', handleSearch);
    locationBtn.addEventListener('click', getCurrentLocation);
    locationInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            handleSearch();
        }
    });

    function handleSearch() {
        var location = locationInput.value.trim();
        if (location === '') {
            showError('Please enter a location');
            return;
        }
        getWeatherData(location);
    }

    function getCurrentLocation() {
        if (!navigator.geolocation) {
            showError('Geolocation not supported');
            return;
        }

        showLoading();
        navigator.geolocation.getCurrentPosition(
            function(position) {
                var lat = position.coords.latitude;
                var lon = position.coords.longitude;
                var coords = lat + ',' + lon;
                locationInput.value = coords;
                getWeatherData(coords);
            },
            function(err) {
                hideLoading();
                if (err.code === err.PERMISSION_DENIED) {
                    showError('Location access denied');
                } else if (err.code === err.POSITION_UNAVAILABLE) {
                    showError('Location unavailable');
                } else if (err.code === err.TIMEOUT) {
                    showError('Location request timed out');
                } else {
                    showError('Error getting location');
                }
            }
        );
    }

    function getWeatherData(location) {
        showLoading();

        // Make API call to our backend
        fetch('/weather', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ location: location })
        })
        .then(function(response) {
            return response.json();
        })
        .then(function(data) {
            hideLoading();
            if (data.error) {
                showError(data.error);
            } else {
                displayWeatherData(data);
            }
        })
        .catch(function(error) {
            hideLoading();
            showError('Failed to get weather data. Try again.');
            console.log('Error:', error);
        });
    }

    function displayWeatherData(data) {
        hideError();

        // Show current weather
        var current = data.current;
        document.getElementById('currentLocation').textContent = current.location;
        document.getElementById('currentTemp').textContent = current.temperature;
        document.getElementById('currentDescription').textContent = current.description;
        document.getElementById('currentIcon').src = 'https://openweathermap.org/img/wn/' + current.icon + '@2x.png';
        document.getElementById('feelsLike').textContent = current.feels_like;
        document.getElementById('humidity').textContent = current.humidity;
        document.getElementById('windSpeed').textContent = current.wind_speed;
        document.getElementById('visibility').textContent = current.visibility;
        document.getElementById('pressure').textContent = current.pressure;
        document.getElementById('sunrise').textContent = current.sunrise;
        document.getElementById('sunset').textContent = current.sunset;

        // Show forecast
        var forecastContainer = document.getElementById('forecastContainer');
        forecastContainer.innerHTML = '';

        if (data.forecast) {
            for (var i = 0; i < data.forecast.length; i++) {
                var forecastItem = createForecastItem(data.forecast[i]);
                forecastContainer.appendChild(forecastItem);
            }
        }

        weatherContainer.classList.remove('hidden');
    }

    function createForecastItem(day) {
        var item = document.createElement('div');
        item.className = 'forecast-item';

        var html = '<div class="forecast-date">' + day.date + '</div>';
        html += '<img class="forecast-icon" src="https://openweathermap.org/img/wn/' + day.icon + '@2x.png" alt="' + day.description + '">';
        html += '<div class="forecast-temps">';
        html += '<span class="temp-max">' + day.temp_max + '°</span>';
        html += '<span class="temp-min">' + day.temp_min + '°</span>';
        html += '</div>';
        html += '<div class="forecast-description">' + day.description + '</div>';
        html += '<div class="forecast-details">';
        html += '<span><i class="fas fa-tint"></i> ' + day.humidity + '%</span>';
        html += '<span><i class="fas fa-wind"></i> ' + day.wind_speed + 'm/s</span>';
        html += '</div>';

        item.innerHTML = html;
        return item;
    }

    function showLoading() {
        loading.classList.remove('hidden');
        error.classList.add('hidden');
        weatherContainer.classList.add('hidden');
    }

    function hideLoading() {
        loading.classList.add('hidden');
    }

    function showError(message) {
        errorMessage.textContent = message;
        error.classList.remove('hidden');
        weatherContainer.classList.add('hidden');
    }

    function hideError() {
        error.classList.add('hidden');
    }
});
