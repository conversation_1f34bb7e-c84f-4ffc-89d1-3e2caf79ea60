document.addEventListener('DOMContentLoaded', function() {
    const locationInput = document.getElementById('locationInput');
    const searchBtn = document.getElementById('searchBtn');
    const locationBtn = document.getElementById('locationBtn');
    const loading = document.getElementById('loading');
    const error = document.getElementById('error');
    const weatherContainer = document.getElementById('weatherContainer');
    const errorMessage = document.getElementById('errorMessage');

    // Event listeners
    searchBtn.addEventListener('click', handleSearch);
    locationBtn.addEventListener('click', getCurrentLocation);
    locationInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            handleSearch();
        }
    });

    function handleSearch() {
        const location = locationInput.value.trim();
        if (!location) {
            showError('Please enter a location');
            return;
        }
        getWeatherData(location);
    }

    function getCurrentLocation() {
        if (!navigator.geolocation) {
            showError('Geolocation is not supported by this browser');
            return;
        }

        showLoading();
        navigator.geolocation.getCurrentPosition(
            function(position) {
                const lat = position.coords.latitude;
                const lon = position.coords.longitude;
                const coordinates = `${lat},${lon}`;
                locationInput.value = coordinates;
                getWeatherData(coordinates);
            },
            function(error) {
                hideLoading();
                switch(error.code) {
                    case error.PERMISSION_DENIED:
                        showError('Location access denied by user');
                        break;
                    case error.POSITION_UNAVAILABLE:
                        showError('Location information is unavailable');
                        break;
                    case error.TIMEOUT:
                        showError('Location request timed out');
                        break;
                    default:
                        showError('An unknown error occurred while retrieving location');
                        break;
                }
            }
        );
    }

    function getWeatherData(location) {
        showLoading();
        
        fetch('/weather', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ location: location })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.error) {
                showError(data.error);
            } else {
                displayWeatherData(data);
            }
        })
        .catch(error => {
            hideLoading();
            showError('Failed to fetch weather data. Please try again.');
            console.error('Error:', error);
        });
    }

    function displayWeatherData(data) {
        hideError();
        
        // Display current weather
        const current = data.current;
        document.getElementById('currentLocation').textContent = current.location;
        document.getElementById('currentTemp').textContent = current.temperature;
        document.getElementById('currentDescription').textContent = current.description;
        document.getElementById('currentIcon').src = `https://openweathermap.org/img/wn/${current.icon}@2x.png`;
        document.getElementById('feelsLike').textContent = current.feels_like;
        document.getElementById('humidity').textContent = current.humidity;
        document.getElementById('windSpeed').textContent = current.wind_speed;
        document.getElementById('visibility').textContent = current.visibility;
        document.getElementById('pressure').textContent = current.pressure;
        document.getElementById('sunrise').textContent = current.sunrise;
        document.getElementById('sunset').textContent = current.sunset;

        // Display forecast
        const forecastContainer = document.getElementById('forecastContainer');
        forecastContainer.innerHTML = '';
        
        if (data.forecast) {
            data.forecast.forEach(day => {
                const forecastItem = createForecastItem(day);
                forecastContainer.appendChild(forecastItem);
            });
        }

        weatherContainer.classList.remove('hidden');
    }

    function createForecastItem(day) {
        const item = document.createElement('div');
        item.className = 'forecast-item';
        
        item.innerHTML = `
            <div class="forecast-date">${day.date}</div>
            <img class="forecast-icon" src="https://openweathermap.org/img/wn/${day.icon}@2x.png" alt="${day.description}">
            <div class="forecast-temps">
                <span class="temp-max">${day.temp_max}°</span>
                <span class="temp-min">${day.temp_min}°</span>
            </div>
            <div class="forecast-description">${day.description}</div>
            <div class="forecast-details">
                <span><i class="fas fa-tint"></i> ${day.humidity}%</span>
                <span><i class="fas fa-wind"></i> ${day.wind_speed}m/s</span>
            </div>
        `;
        
        return item;
    }

    function showLoading() {
        loading.classList.remove('hidden');
        error.classList.add('hidden');
        weatherContainer.classList.add('hidden');
    }

    function hideLoading() {
        loading.classList.add('hidden');
    }

    function showError(message) {
        errorMessage.textContent = message;
        error.classList.remove('hidden');
        weatherContainer.classList.add('hidden');
    }

    function hideError() {
        error.classList.add('hidden');
    }
});
