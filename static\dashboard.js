// Advanced Dashboard Functionality

let currentPage = 1;
let totalPages = 1;

// Tab Management
function showTab(tabName) {
    // Hide all tab contents
    var tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(function(content) {
        content.classList.remove('active');
    });
    
    // Remove active class from all tabs
    var tabs = document.querySelectorAll('.tab');
    tabs.forEach(function(tab) {
        tab.classList.remove('active');
    });
    
    // Show selected tab content
    document.getElementById(tabName).classList.add('active');
    
    // Add active class to clicked tab
    event.target.classList.add('active');
    
    // Load data for specific tabs
    if (tabName === 'weather-requests') {
        loadWeatherRequests();
    }
}

// Weather Requests Management
function loadWeatherRequests(page = 1) {
    showLoading();
    
    fetch(`/api/weather-requests?page=${page}&per_page=5`)
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.error) {
                showError(data.error);
            } else {
                displayWeatherRequests(data);
                currentPage = data.current_page;
                totalPages = data.pages;
                updatePagination();
            }
        })
        .catch(error => {
            hideLoading();
            showError('Failed to load weather requests');
            console.error('Error:', error);
        });
}

function displayWeatherRequests(data) {
    var container = document.getElementById('weatherRequestsList');
    container.innerHTML = '';
    
    if (data.requests.length === 0) {
        container.innerHTML = '<p>No weather requests found. Create your first request!</p>';
        return;
    }
    
    data.requests.forEach(function(request) {
        var requestItem = createWeatherRequestItem(request);
        container.appendChild(requestItem);
    });
}

function createWeatherRequestItem(request) {
    var item = document.createElement('div');
    item.className = 'weather-request-item';
    
    var weatherSummary = '';
    if (request.weather_data && request.weather_data.length > 0) {
        var current = request.weather_data.find(item => item.type === 'current');
        if (current) {
            weatherSummary = `${current.temperature}°C, ${current.description}`;
        }
    }
    
    item.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: start;">
            <div style="flex: 1;">
                <h3>${request.normalized_location}</h3>
                <p><strong>Date Range:</strong> ${request.start_date} to ${request.end_date}</p>
                <p><strong>Requested:</strong> ${new Date(request.request_date).toLocaleDateString()}</p>
                ${request.user_notes ? `<p><strong>Notes:</strong> ${request.user_notes}</p>` : ''}
                ${weatherSummary ? `<p><strong>Weather:</strong> ${weatherSummary}</p>` : ''}
            </div>
            <div style="display: flex; gap: 10px; flex-direction: column;">
                <button class="btn btn-info" onclick="viewLocationInfo(${request.id})">
                    <i class="fas fa-map"></i> View Location
                </button>
                <button class="btn btn-primary" onclick="editWeatherRequest(${request.id})">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button class="btn btn-danger" onclick="deleteWeatherRequest(${request.id})">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        </div>
    `;
    
    return item;
}

function updatePagination() {
    var container = document.getElementById('requestsPagination');
    container.innerHTML = '';
    
    if (totalPages <= 1) return;
    
    // Previous button
    if (currentPage > 1) {
        var prevBtn = document.createElement('button');
        prevBtn.className = 'btn btn-primary';
        prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i> Previous';
        prevBtn.onclick = function() { loadWeatherRequests(currentPage - 1); };
        container.appendChild(prevBtn);
    }
    
    // Page numbers
    for (var i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        var pageBtn = document.createElement('button');
        pageBtn.className = i === currentPage ? 'btn btn-success' : 'btn btn-primary';
        pageBtn.textContent = i;
        pageBtn.onclick = (function(page) {
            return function() { loadWeatherRequests(page); };
        })(i);
        container.appendChild(pageBtn);
    }
    
    // Next button
    if (currentPage < totalPages) {
        var nextBtn = document.createElement('button');
        nextBtn.className = 'btn btn-primary';
        nextBtn.innerHTML = 'Next <i class="fas fa-chevron-right"></i>';
        nextBtn.onclick = function() { loadWeatherRequests(currentPage + 1); };
        container.appendChild(nextBtn);
    }
}

// Create Weather Request
document.addEventListener('DOMContentLoaded', function() {
    var createForm = document.getElementById('createRequestForm');
    if (createForm) {
        createForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            var formData = {
                location: document.getElementById('requestLocation').value,
                start_date: document.getElementById('startDate').value,
                end_date: document.getElementById('endDate').value,
                user_notes: document.getElementById('userNotes').value
            };
            
            showLoading();
            
            fetch('/api/weather-requests', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.error) {
                    showError(data.error);
                } else {
                    alert('Weather request created successfully!');
                    createForm.reset();
                    // Switch to weather requests tab
                    showTab('weather-requests');
                }
            })
            .catch(error => {
                hideLoading();
                showError('Failed to create weather request');
                console.error('Error:', error);
            });
        });
    }
});

// Edit Weather Request
function editWeatherRequest(requestId) {
    fetch(`/api/weather-requests/${requestId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showError(data.error);
            } else {
                // Populate edit form
                document.getElementById('editRequestId').value = data.id;
                document.getElementById('editLocation').value = data.location;
                document.getElementById('editStartDate').value = data.start_date;
                document.getElementById('editEndDate').value = data.end_date;
                document.getElementById('editUserNotes').value = data.user_notes || '';
                
                // Show modal
                document.getElementById('editModal').style.display = 'block';
            }
        })
        .catch(error => {
            showError('Failed to load weather request');
            console.error('Error:', error);
        });
}

function closeEditModal() {
    document.getElementById('editModal').style.display = 'none';
}

// Handle edit form submission
document.addEventListener('DOMContentLoaded', function() {
    var editForm = document.getElementById('editRequestForm');
    if (editForm) {
        editForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            var requestId = document.getElementById('editRequestId').value;
            var formData = {
                location: document.getElementById('editLocation').value,
                start_date: document.getElementById('editStartDate').value,
                end_date: document.getElementById('editEndDate').value,
                user_notes: document.getElementById('editUserNotes').value
            };
            
            fetch(`/api/weather-requests/${requestId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showError(data.error);
                } else {
                    alert('Weather request updated successfully!');
                    closeEditModal();
                    loadWeatherRequests(currentPage);
                }
            })
            .catch(error => {
                showError('Failed to update weather request');
                console.error('Error:', error);
            });
        });
    }
});

// Delete Weather Request
function deleteWeatherRequest(requestId) {
    if (confirm('Are you sure you want to delete this weather request?')) {
        fetch(`/api/weather-requests/${requestId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showError(data.error);
            } else {
                alert('Weather request deleted successfully!');
                loadWeatherRequests(currentPage);
            }
        })
        .catch(error => {
            showError('Failed to delete weather request');
            console.error('Error:', error);
        });
    }
}

// Export Data
function exportData(format) {
    window.open(`/api/export/${format}`, '_blank');
}

// View Location Info
function viewLocationInfo(requestId) {
    fetch(`/api/location-info/${requestId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showError(data.error);
            } else {
                var info = `Location: ${data.location}\n`;
                info += `Coordinates: ${data.latitude}, ${data.longitude}\n\n`;
                info += `Google Maps: ${data.google_maps_url}\n`;
                info += `YouTube Search: ${data.youtube_search_url}`;
                
                alert(info);
                
                // Open Google Maps in new tab
                window.open(data.google_maps_url, '_blank');
            }
        })
        .catch(error => {
            showError('Failed to load location info');
            console.error('Error:', error);
        });
}

// Close modal when clicking outside
window.onclick = function(event) {
    var modal = document.getElementById('editModal');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
}
