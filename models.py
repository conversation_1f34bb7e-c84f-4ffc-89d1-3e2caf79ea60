from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import json

db = SQLAlchemy()

class WeatherRequest(db.Model):
    """Model for storing weather requests and data"""
    __tablename__ = 'weather_requests'
    
    id = db.Column(db.Integer, primary_key=True)
    location = db.Column(db.String(200), nullable=False)
    normalized_location = db.Column(db.String(200), nullable=False)  # Standardized location name
    latitude = db.Column(db.Float, nullable=True)
    longitude = db.Column(db.Float, nullable=True)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    request_date = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Weather data (stored as JSON)
    weather_data = db.Column(db.Text, nullable=True)  # JSON string
    
    # Additional metadata
    user_notes = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.<PERSON><PERSON><PERSON>, default=True)
    
    def __init__(self, location, start_date, end_date, normalized_location=None, 
                 latitude=None, longitude=None, weather_data=None, user_notes=None):
        self.location = location
        self.normalized_location = normalized_location or location
        self.latitude = latitude
        self.longitude = longitude
        self.start_date = start_date
        self.end_date = end_date
        self.weather_data = weather_data
        self.user_notes = user_notes
    
    def set_weather_data(self, data):
        """Store weather data as JSON string"""
        if data:
            self.weather_data = json.dumps(data)
    
    def get_weather_data(self):
        """Retrieve weather data as Python object"""
        if self.weather_data:
            try:
                return json.loads(self.weather_data)
            except json.JSONDecodeError:
                return None
        return None
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'location': self.location,
            'normalized_location': self.normalized_location,
            'latitude': self.latitude,
            'longitude': self.longitude,
            'start_date': self.start_date.isoformat() if self.start_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'request_date': self.request_date.isoformat() if self.request_date else None,
            'weather_data': self.get_weather_data(),
            'user_notes': self.user_notes,
            'is_active': self.is_active
        }
    
    def __repr__(self):
        return f'<WeatherRequest {self.id}: {self.location} ({self.start_date} to {self.end_date})>'


class LocationCache(db.Model):
    """Cache for location validation and normalization"""
    __tablename__ = 'location_cache'
    
    id = db.Column(db.Integer, primary_key=True)
    search_term = db.Column(db.String(200), unique=True, nullable=False)
    normalized_name = db.Column(db.String(200), nullable=False)
    latitude = db.Column(db.Float, nullable=False)
    longitude = db.Column(db.Float, nullable=False)
    country = db.Column(db.String(100), nullable=True)
    state = db.Column(db.String(100), nullable=True)
    created_date = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __init__(self, search_term, normalized_name, latitude, longitude, 
                 country=None, state=None):
        self.search_term = search_term.lower()
        self.normalized_name = normalized_name
        self.latitude = latitude
        self.longitude = longitude
        self.country = country
        self.state = state
    
    def to_dict(self):
        return {
            'id': self.id,
            'search_term': self.search_term,
            'normalized_name': self.normalized_name,
            'latitude': self.latitude,
            'longitude': self.longitude,
            'country': self.country,
            'state': self.state
        }
    
    def __repr__(self):
        return f'<LocationCache {self.search_term} -> {self.normalized_name}>'
