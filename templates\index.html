<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weather App</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-cloud-sun"></i> Weather App</h1>
            <p>Get current weather and 5-day forecast for any location</p>
        </header>

        <div class="search-section">
            <div class="search-container">
                <input type="text" id="locationInput" placeholder="Enter city, zip code, or coordinates (lat,lon)">
                <button id="searchBtn"><i class="fas fa-search"></i></button>
                <button id="locationBtn" title="Use current location"><i class="fas fa-location-arrow"></i></button>
            </div>
            <div class="search-examples">
                <p>Examples: "New York", "10001", "40.7128,-74.0060", "London, UK"</p>
            </div>
        </div>

        <div id="loading" class="loading hidden">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Getting weather data...</p>
        </div>

        <div id="error" class="error hidden">
            <i class="fas fa-exclamation-triangle"></i>
            <p id="errorMessage"></p>
        </div>

        <div id="weatherContainer" class="weather-container hidden">
            <!-- Current Weather -->
            <div class="current-weather">
                <div class="weather-header">
                    <h2 id="currentLocation"></h2>
                    <div class="weather-main">
                        <img id="currentIcon" src="" alt="Weather icon">
                        <div class="temperature">
                            <span id="currentTemp"></span>°C
                        </div>
                    </div>
                    <p id="currentDescription"></p>
                </div>
                
                <div class="weather-details">
                    <div class="detail-item">
                        <i class="fas fa-thermometer-half"></i>
                        <span>Feels like</span>
                        <span id="feelsLike"></span>°C
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-tint"></i>
                        <span>Humidity</span>
                        <span id="humidity"></span>%
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-wind"></i>
                        <span>Wind</span>
                        <span id="windSpeed"></span> m/s
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-eye"></i>
                        <span>Visibility</span>
                        <span id="visibility"></span> km
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-compress-arrows-alt"></i>
                        <span>Pressure</span>
                        <span id="pressure"></span> hPa
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-sun"></i>
                        <span>Sunrise</span>
                        <span id="sunrise"></span>
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-moon"></i>
                        <span>Sunset</span>
                        <span id="sunset"></span>
                    </div>
                </div>
            </div>

            <!-- 5-Day Forecast -->
            <div class="forecast-section">
                <h3><i class="fas fa-calendar-alt"></i> 5-Day Forecast</h3>
                <div id="forecastContainer" class="forecast-container">
                    <!-- Forecast items will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
