* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background: linear-gradient(to bottom, #4a90e2, #357abd);
    min-height: 100vh;
    color: #333;
}

/* Main container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.search-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.search-container {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

#locationInput {
    flex: 1;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

#locationInput:focus {
    outline: none;
    border-color: #74b9ff;
}

button {
    padding: 15px 20px;
    border: none;
    border-radius: 8px;
    background: #4a90e2;
    color: white;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.2s ease;
}

button:hover {
    background: #357abd;
    transform: translateY(-1px);
}

#locationBtn {
    background: #28a745;
}

#locationBtn:hover {
    background: #218838;
}

.search-examples {
    text-align: center;
    color: #666;
    font-size: 0.9rem;
}

.loading, .error {
    text-align: center;
    padding: 30px;
    background: white;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.loading i {
    font-size: 2rem;
    color: #74b9ff;
    margin-bottom: 15px;
}

.error {
    background: #ffe0e0;
    color: #d63031;
}

.error i {
    font-size: 2rem;
    margin-bottom: 15px;
}

.hidden {
    display: none;
}

.weather-container {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.current-weather {
    padding: 30px;
    background: linear-gradient(45deg, #4a90e2, #357abd);
    color: white;
}

.weather-header {
    text-align: center;
    margin-bottom: 30px;
}

.weather-header h2 {
    font-size: 1.8rem;
    margin-bottom: 20px;
}

.weather-main {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-bottom: 15px;
}

.weather-main img {
    width: 80px;
    height: 80px;
}

.temperature {
    font-size: 3rem;
    font-weight: bold;
}

.weather-header p {
    font-size: 1.2rem;
    text-transform: capitalize;
    opacity: 0.9;
}

.weather-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255,255,255,0.1);
    padding: 15px;
    border-radius: 10px;
}

.detail-item i {
    width: 20px;
    text-align: center;
}

.forecast-section {
    padding: 30px;
}

.forecast-section h3 {
    margin-bottom: 25px;
    color: #333;
    font-size: 1.5rem;
}

.forecast-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.forecast-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    transition: transform 0.3s;
}

.forecast-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.forecast-date {
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
}

.forecast-icon {
    width: 50px;
    height: 50px;
    margin: 10px auto;
}

.forecast-temps {
    display: flex;
    justify-content: space-between;
    margin: 15px 0;
    font-weight: bold;
}

.temp-max {
    color: #d63031;
}

.temp-min {
    color: #74b9ff;
}

.forecast-description {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 10px;
    text-transform: capitalize;
}

.forecast-details {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #888;
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    header h1 {
        font-size: 2rem;
    }

    .search-container {
        flex-direction: column;
    }

    .weather-main {
        flex-direction: column;
        gap: 10px;
    }

    .temperature {
        font-size: 2.5rem;
    }

    .weather-details {
        grid-template-columns: 1fr;
    }

    .forecast-container {
        grid-template-columns: 1fr;
    }
}
