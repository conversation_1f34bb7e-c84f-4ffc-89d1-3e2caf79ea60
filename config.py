# Configuration file for Weather App
import os

# OpenWeatherMap API Key
# Get your free API key from: https://openweathermap.org/api
API_KEY = os.environ.get('OPENWEATHER_API_KEY','********************************')

# API Base URL
BASE_URL = "http://api.openweathermap.org/data/2.5"

# Database configuration
SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL', 'sqlite:///weather_app.db')
SQLALCHEMY_TRACK_MODIFICATIONS = False

# Flask configuration
DEBUG = True
PORT = 5000
SECRET_KEY = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')

# External API Keys (optional features)
YOUTUBE_API_KEY = os.environ.get('YOUTUBE_API_KEY', '')
GOOGLE_MAPS_API_KEY = os.environ.get('GOOGLE_MAPS_API_KEY', '')
