# Advanced Weather Dashboard

Complete weather management system with CRUD operations, data persistence, and export capabilities.

## Core Features

### Basic Weather Functionality
- Search weather by city name, zip code, or coordinates
- Shows current temperature, humidity, wind speed, etc.
- 5-day forecast with highs/lows
- Use your current location
- Works on mobile and desktop

### Advanced CRUD Operations
- **CREATE**: Save weather requests with location and date range validation
- **READ**: View all saved weather requests with pagination
- **UPDATE**: Edit existing weather requests and refresh weather data
- **DELETE**: Remove weather requests (soft delete)

### Data Validation & Location Intelligence
- Date range validation (prevents invalid date ranges)
- Location validation with fuzzy matching suggestions
- Location caching for improved performance
- Coordinate support with reverse geocoding

### Data Export (Multiple Formats)
- **JSON**: Structured data export
- **CSV**: Spreadsheet-compatible format
- **XML**: Structured markup export
- **PDF**: Professional report format
- **Markdown**: Documentation-friendly format

### Optional API Integrations
- Google Maps integration for location visualization
- YouTube search links for location-based content
- Extensible architecture for additional APIs

## Tech Stack

### Backend
- **Flask**: Web framework with SQLAlchemy ORM
- **SQLite**: Database for data persistence
- **Python Libraries**:
  - `python-dateutil`: Date parsing and validation
  - `reportlab`: PDF generation
  - `difflib`: Fuzzy location matching

### Frontend
- **HTML5/CSS3/JavaScript**: Modern responsive interface
- **Font Awesome**: Icons and UI elements
- **Tabbed Interface**: Multi-section dashboard

### APIs & Integrations
- **OpenWeatherMap API**: Real-time weather data
- **Google Maps API**: Location visualization (optional)
- **YouTube API**: Location-based content (optional)

## Setup & Installation

### Prerequisites
- Python 3.7 or higher
- OpenWeatherMap API key (free at https://openweathermap.org/api)

### Quick Start

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Get API key:**
   - Visit https://openweathermap.org/api
   - Sign up for free account
   - Copy your API key

3. **Configure API key:**
   ```bash
   # Option 1: Environment variable (recommended)
   # Windows
   set OPENWEATHER_API_KEY=your_actual_api_key_here

   # Mac/Linux
   export OPENWEATHER_API_KEY=your_actual_api_key_here

   # Option 2: Edit config.py directly
   # Change the API_KEY value in config.py
   ```

4. **Run the application:**
   ```bash
   python app.py
   ```

5. **Access the dashboard:**
   - Open http://localhost:5000 for the full dashboard
   - Or http://localhost:5000/simple for the basic weather view

## Usage Guide

### Dashboard Navigation
The application features a tabbed interface with four main sections:

1. **Current Weather**: Real-time weather lookup (original functionality)
2. **Weather Requests**: View and manage saved weather data
3. **Create Request**: Add new weather requests with date ranges
4. **Export Data**: Download weather data in various formats

### Location Input Formats
- **City Names**: "New York", "Paris", "Tokyo"
- **City with Country**: "London, UK", "Sydney, Australia"
- **ZIP/Postal Codes**: "10001", "SW1A 1AA"
- **Coordinates**: "40.7128,-74.0060" (latitude,longitude)
- **Landmarks**: "Times Square", "Eiffel Tower"

### CRUD Operations

#### Creating Weather Requests
1. Go to "Create Request" tab
2. Enter location (validated with fuzzy matching)
3. Select date range (validated for realistic dates)
4. Add optional notes
5. Submit to save with weather data

#### Reading Weather Requests
- View all saved requests in "Weather Requests" tab
- Paginated display (10 requests per page)
- See location, dates, notes, and weather summary

#### Updating Weather Requests
- Click "Edit" button on any request
- Modify location, dates, or notes
- Weather data automatically refreshes

#### Deleting Weather Requests
- Click "Delete" button on any request
- Soft delete (data preserved but hidden)

### Data Export Options
Export your weather data in multiple formats:
- **JSON**: For programmatic use
- **CSV**: For spreadsheet analysis
- **XML**: For structured data exchange
- **PDF**: For professional reports
- **Markdown**: For documentation

### API Endpoints

#### Weather Requests CRUD
```bash
# Create new weather request
POST /api/weather-requests
{
  "location": "New York",
  "start_date": "2024-06-01",
  "end_date": "2024-06-05",
  "user_notes": "Business trip weather"
}

# Get all weather requests (paginated)
GET /api/weather-requests?page=1&per_page=10

# Get specific weather request
GET /api/weather-requests/{id}

# Update weather request
PUT /api/weather-requests/{id}
{
  "location": "Updated Location",
  "user_notes": "Updated notes"
}

# Delete weather request
DELETE /api/weather-requests/{id}
```

#### Data Export
```bash
# Export in various formats
GET /api/export/json
GET /api/export/csv
GET /api/export/xml
GET /api/export/pdf
GET /api/export/markdown
```

#### Location Information
```bash
# Get additional location info (maps, videos)
GET /api/location-info/{request_id}
```

## API Information

This app uses the OpenWeatherMap API which provides:
- Current weather data for any location
- 5-day weather forecasts
- Weather icons and descriptions
- Global coverage with high accuracy

## File Structure

```
weather_app/
├── app.py              # Main Flask application
├── config.py           # Configuration settings
├── requirements.txt    # Python dependencies
├── README.md          # This file
├── templates/
│   └── index.html     # Main HTML template
└── static/
    ├── style.css      # CSS styling
    └── script.js      # JavaScript functionality
```

## Troubleshooting

### Common Issues

1. **"Location not found" error**
   - Check spelling of location name
   - Try adding country code (e.g., "London, UK")
   - Use coordinates for precise locations

2. **API key errors**
   - Ensure your API key is valid and active
   - Check that you've set the environment variable correctly
   - New API keys may take a few minutes to activate

3. **Geolocation not working**
   - Ensure you're using HTTPS (or localhost)
   - Allow location access when prompted by browser
   - Some browsers block geolocation on HTTP sites

### Development Notes

- The app runs in debug mode by default for development
- Weather icons are loaded from OpenWeatherMap's CDN
- All temperatures are displayed in Celsius
- Wind speeds are in meters per second

## License

This project is open source and available under the MIT License.
