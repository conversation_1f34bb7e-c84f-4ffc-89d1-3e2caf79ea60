# Weather App

A modern, responsive weather application that provides current weather conditions and 5-day forecasts for any location worldwide.

## Features

### Core Features
- **Location Input Support**: Enter locations in multiple formats:
  - City names (e.g., "New York", "London, UK")
  - ZIP/Postal codes (e.g., "10001", "SW1A 1AA")
  - GPS coordinates (e.g., "40.7128,-74.0060")
  - Landmarks and addresses

- **Current Weather Display**: Shows comprehensive weather information including:
  - Temperature and "feels like" temperature
  - Weather description with icons
  - Humidity, pressure, and visibility
  - Wind speed and direction
  - Sunrise and sunset times

### Enhanced Features
- **5-Day Forecast**: Extended weather predictions with daily high/low temperatures
- **Geolocation Support**: Get weather for your current location with one click
- **Weather Icons**: Visual representation of weather conditions
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Real-time Data**: Uses OpenWeatherMap API for accurate, up-to-date weather information

## Technology Stack

- **Backend**: Flask (Python)
- **Frontend**: HTML5, CSS3, JavaScript
- **API**: OpenWeatherMap API
- **Icons**: Font Awesome
- **Styling**: Modern CSS with gradients and animations

## Setup Instructions

### Prerequisites
- Python 3.7 or higher
- OpenWeatherMap API key (free at https://openweathermap.org/api)

### Installation

1. **Clone or download the project**
   ```bash
   cd weather_app
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Get an API key**
   - Visit https://openweathermap.org/api
   - Sign up for a free account
   - Get your API key from the dashboard

4. **Configure the API key**
   
   **Option 1: Environment Variable (Recommended)**
   ```bash
   # Windows
   set OPENWEATHER_API_KEY=your_actual_api_key_here
   
   # macOS/Linux
   export OPENWEATHER_API_KEY=your_actual_api_key_here
   ```
   
   **Option 2: Edit config.py**
   ```python
   API_KEY = "your_actual_api_key_here"
   ```

5. **Run the application**
   ```bash
   python app.py
   ```

6. **Open your browser**
   - Navigate to http://localhost:5000
   - Start searching for weather!

## Usage Examples

### Location Input Formats
- **City**: "New York", "Paris", "Tokyo"
- **City with Country**: "London, UK", "Sydney, Australia"
- **ZIP Code**: "10001", "90210"
- **Coordinates**: "40.7128,-74.0060" (latitude,longitude)
- **Landmarks**: "Times Square", "Eiffel Tower"

### Features Demo
1. **Search by City**: Type "London" and click search
2. **Use Current Location**: Click the location button (📍) to get weather for your current position
3. **View Forecast**: Scroll down to see the 5-day forecast with daily highs/lows
4. **Mobile Friendly**: Try it on your phone - it's fully responsive!

## API Information

This app uses the OpenWeatherMap API which provides:
- Current weather data for any location
- 5-day weather forecasts
- Weather icons and descriptions
- Global coverage with high accuracy

## File Structure

```
weather_app/
├── app.py              # Main Flask application
├── config.py           # Configuration settings
├── requirements.txt    # Python dependencies
├── README.md          # This file
├── templates/
│   └── index.html     # Main HTML template
└── static/
    ├── style.css      # CSS styling
    └── script.js      # JavaScript functionality
```

## Troubleshooting

### Common Issues

1. **"Location not found" error**
   - Check spelling of location name
   - Try adding country code (e.g., "London, UK")
   - Use coordinates for precise locations

2. **API key errors**
   - Ensure your API key is valid and active
   - Check that you've set the environment variable correctly
   - New API keys may take a few minutes to activate

3. **Geolocation not working**
   - Ensure you're using HTTPS (or localhost)
   - Allow location access when prompted by browser
   - Some browsers block geolocation on HTTP sites

### Development Notes

- The app runs in debug mode by default for development
- Weather icons are loaded from OpenWeatherMap's CDN
- All temperatures are displayed in Celsius
- Wind speeds are in meters per second

## License

This project is open source and available under the MIT License.
