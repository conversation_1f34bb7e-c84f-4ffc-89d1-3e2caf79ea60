# Weather Dashboard

Simple weather app that shows current conditions and 5-day forecast.

## What it does

- Search weather by city name, zip code, or coordinates
- Shows current temperature, humidity, wind speed, etc.
- 5-day forecast with highs/lows
- Use your current location
- Works on mobile and desktop

## Features

- Multiple location formats supported
- Real-time weather data from OpenWeatherMap
- Clean, responsive interface
- Geolocation support

## Tech Stack

- Flask (Python backend)
- HTML/CSS/JavaScript frontend
- OpenWeatherMap API
- Font Awesome icons

## Setup

You'll need Python and an OpenWeatherMap API key.

1. Install requirements:
   ```
   pip install -r requirements.txt
   ```

2. Get API key from https://openweathermap.org/api

3. Set your API key:
   ```
   # Windows
   set OPENWEATHER_API_KEY=your_key_here

   # Mac/Linux
   export OPENWEATHER_API_KEY=your_key_here
   ```

   Or edit config.py and put your key there.

4. Run it:
   ```
   python app.py
   ```

5. Open http://localhost:5000

## Usage Examples

### Location Input Formats
- **City**: "New York", "Paris", "Tokyo"
- **City with Country**: "London, UK", "Sydney, Australia"
- **ZIP Code**: "10001", "90210"
- **Coordinates**: "40.7128,-74.0060" (latitude,longitude)
- **Landmarks**: "Times Square", "Eiffel Tower"

### Features Demo
1. **Search by City**: Type "London" and click search
2. **Use Current Location**: Click the location button (📍) to get weather for your current position
3. **View Forecast**: Scroll down to see the 5-day forecast with daily highs/lows
4. **Mobile Friendly**: Try it on your phone - it's fully responsive!

## API Information

This app uses the OpenWeatherMap API which provides:
- Current weather data for any location
- 5-day weather forecasts
- Weather icons and descriptions
- Global coverage with high accuracy

## File Structure

```
weather_app/
├── app.py              # Main Flask application
├── config.py           # Configuration settings
├── requirements.txt    # Python dependencies
├── README.md          # This file
├── templates/
│   └── index.html     # Main HTML template
└── static/
    ├── style.css      # CSS styling
    └── script.js      # JavaScript functionality
```

## Troubleshooting

### Common Issues

1. **"Location not found" error**
   - Check spelling of location name
   - Try adding country code (e.g., "London, UK")
   - Use coordinates for precise locations

2. **API key errors**
   - Ensure your API key is valid and active
   - Check that you've set the environment variable correctly
   - New API keys may take a few minutes to activate

3. **Geolocation not working**
   - Ensure you're using HTTPS (or localhost)
   - Allow location access when prompted by browser
   - Some browsers block geolocation on HTTP sites

### Development Notes

- The app runs in debug mode by default for development
- Weather icons are loaded from OpenWeatherMap's CDN
- All temperatures are displayed in Celsius
- Wind speeds are in meters per second

## License

This project is open source and available under the MIT License.
