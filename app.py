from flask import Flask, render_template, request, jsonify, send_file, redirect, url_for, flash
import requests
import os
from datetime import datetime, date, timedelta
import json
import config
import csv
import io
from dateutil import parser
from difflib import get_close_matches
import xml.etree.ElementTree as ET
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib import colors
from models import db, WeatherRequest, LocationCache
from export_utils import export_to_json, export_to_csv, export_to_xml, export_to_pdf, export_to_markdown

app = Flask(__name__)

# Configuration
app.config['SQLALCHEMY_DATABASE_URI'] = config.SQLALCHEMY_DATABASE_URI
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = config.SQLALCHEMY_TRACK_MODIFICATIONS
app.config['SECRET_KEY'] = config.SECRET_KEY

# Initialize database
db.init_app(app)

# TODO: move this to environment variables later
API_KEY = config.API_KEY
BASE_URL = config.BASE_URL

def get_weather_data(location):
    """Get current weather data for a location"""
    try:
        # Check if it's coordinates (lat,lon format)
        if ',' in location:
            coords = location.split(',')
            lat = coords[0].strip()
            lon = coords[1].strip()
            url = f"{BASE_URL}/weather?lat={lat}&lon={lon}&appid={API_KEY}&units=metric"
        else:
            # Regular location search
            url = f"{BASE_URL}/weather?q={location}&appid={API_KEY}&units=metric"

        resp = requests.get(url)
        if resp.status_code == 200:
            return resp.json()
        else:
            print(f"API returned status code: {resp.status_code}")
            return None
    except Exception as e:
        print(f"Error: {e}")
        return None

def get_forecast_data(location):
    """Get 5-day forecast data for a location"""
    try:
        # Same logic as weather data - maybe should refactor this later
        if ',' in location:
            coords = location.split(',')
            lat = coords[0].strip()
            lon = coords[1].strip()
            url = f"{BASE_URL}/forecast?lat={lat}&lon={lon}&appid={API_KEY}&units=metric"
        else:
            url = f"{BASE_URL}/forecast?q={location}&appid={API_KEY}&units=metric"

        response = requests.get(url)
        if response.status_code == 200:
            return response.json()
        else:
            return None
    except Exception as e:
        print(f"Error fetching forecast data: {e}")
        return None

def format_weather_data(weather_data):
    if not weather_data:
        return None

    # Extract main weather info
    main = weather_data['main']
    weather = weather_data['weather'][0]
    sys_data = weather_data['sys']

    formatted = {
        'location': f"{weather_data['name']}, {sys_data['country']}",
        'temperature': round(main['temp']),
        'feels_like': round(main['feels_like']),
        'description': weather['description'].title(),
        'icon': weather['icon'],
        'humidity': main['humidity'],
        'pressure': main['pressure'],
        'wind_speed': weather_data['wind']['speed'],
        'wind_direction': weather_data['wind'].get('deg', 0),
        'sunrise': datetime.fromtimestamp(sys_data['sunrise']).strftime('%H:%M'),
        'sunset': datetime.fromtimestamp(sys_data['sunset']).strftime('%H:%M'),
    }

    # Handle visibility (not always present)
    if 'visibility' in weather_data:
        formatted['visibility'] = weather_data['visibility'] / 1000
    else:
        formatted['visibility'] = 0

    return formatted

def format_forecast_data(forecast_data):
    if not forecast_data:
        return None

    daily_data = {}

    # Group forecast data by date
    for item in forecast_data['list']:
        dt = datetime.fromtimestamp(item['dt'])
        date_key = dt.strftime('%Y-%m-%d')

        if date_key not in daily_data:
            daily_data[date_key] = {
                'date': dt.strftime('%A, %B %d'),
                'temps': [],
                'descriptions': [],
                'icons': [],
                'humidity_vals': [],
                'wind_vals': []
            }

        daily_data[date_key]['temps'].append(item['main']['temp'])
        daily_data[date_key]['descriptions'].append(item['weather'][0]['description'])
        daily_data[date_key]['icons'].append(item['weather'][0]['icon'])
        daily_data[date_key]['humidity_vals'].append(item['main']['humidity'])
        daily_data[date_key]['wind_vals'].append(item['wind']['speed'])

    # Build final forecast list
    forecast_list = []
    count = 0
    for date_key, data in daily_data.items():
        if count >= 5:  # Only want 5 days
            break

        # Find most common description and icon
        desc_counts = {}
        for desc in data['descriptions']:
            desc_counts[desc] = desc_counts.get(desc, 0) + 1
        most_common_desc = max(desc_counts, key=desc_counts.get)

        icon_counts = {}
        for icon in data['icons']:
            icon_counts[icon] = icon_counts.get(icon, 0) + 1
        most_common_icon = max(icon_counts, key=icon_counts.get)

        forecast_item = {
            'date': data['date'],
            'temp_max': round(max(data['temps'])),
            'temp_min': round(min(data['temps'])),
            'description': most_common_desc.title(),
            'icon': most_common_icon,
            'humidity': round(sum(data['humidity_vals']) / len(data['humidity_vals'])),
            'wind_speed': round(sum(data['wind_vals']) / len(data['wind_vals']), 1)
        }

        forecast_list.append(forecast_item)
        count += 1

    return forecast_list

# Advanced utility functions for CRUD operations
def validate_date_range(start_date_str, end_date_str):
    """Validate and parse date range"""
    try:
        start_date = parser.parse(start_date_str).date()
        end_date = parser.parse(end_date_str).date()

        # Check if dates are valid
        if start_date > end_date:
            return None, None, "Start date cannot be after end date"

        # Check if date range is not too far in the future
        max_future_date = date.today() + timedelta(days=5)
        if start_date > max_future_date:
            return None, None, "Start date cannot be more than 5 days in the future"

        # Check if date range is not too far in the past (OpenWeatherMap limitation)
        min_past_date = date.today() - timedelta(days=5)
        if end_date < min_past_date:
            return None, None, "End date cannot be more than 5 days in the past"

        return start_date, end_date, None
    except (ValueError, TypeError) as e:
        return None, None, f"Invalid date format: {str(e)}"

def validate_and_normalize_location(location):
    """Validate location and return normalized data with fuzzy matching"""
    if not location or len(location.strip()) < 2:
        return None, "Location must be at least 2 characters long"

    location = location.strip()

    # Check cache first
    cached = LocationCache.query.filter_by(search_term=location.lower()).first()
    if cached:
        return cached.to_dict(), None

    # Try to get location data from OpenWeatherMap
    try:
        # Check if it's coordinates
        if ',' in location:
            coords = location.split(',')
            if len(coords) == 2:
                try:
                    lat = float(coords[0].strip())
                    lon = float(coords[1].strip())
                    if -90 <= lat <= 90 and -180 <= lon <= 180:
                        # Reverse geocoding to get location name
                        url = f"{BASE_URL}/weather?lat={lat}&lon={lon}&appid={API_KEY}"
                        response = requests.get(url)
                        if response.status_code == 200:
                            data = response.json()
                            normalized_name = f"{data['name']}, {data['sys']['country']}"

                            # Cache the result
                            cache_entry = LocationCache(
                                search_term=location.lower(),
                                normalized_name=normalized_name,
                                latitude=lat,
                                longitude=lon,
                                country=data['sys']['country']
                            )
                            db.session.add(cache_entry)
                            db.session.commit()

                            return cache_entry.to_dict(), None
                except ValueError:
                    pass

        # Regular location search
        url = f"{BASE_URL}/weather?q={location}&appid={API_KEY}"
        response = requests.get(url)

        if response.status_code == 200:
            data = response.json()
            normalized_name = f"{data['name']}, {data['sys']['country']}"

            # Cache the result
            cache_entry = LocationCache(
                search_term=location.lower(),
                normalized_name=normalized_name,
                latitude=data['coord']['lat'],
                longitude=data['coord']['lon'],
                country=data['sys']['country']
            )
            db.session.add(cache_entry)
            db.session.commit()

            return cache_entry.to_dict(), None

        elif response.status_code == 404:
            # Try fuzzy matching with cached locations
            all_locations = LocationCache.query.all()
            location_names = [loc.search_term for loc in all_locations]
            matches = get_close_matches(location.lower(), location_names, n=3, cutoff=0.6)

            if matches:
                suggestions = []
                for match in matches:
                    cached_loc = LocationCache.query.filter_by(search_term=match).first()
                    if cached_loc:
                        suggestions.append(cached_loc.normalized_name)

                return None, f"Location not found. Did you mean: {', '.join(suggestions)}?"

            return None, "Location not found. Please check spelling or try a different location."

        else:
            return None, f"Error validating location: API returned status {response.status_code}"

    except Exception as e:
        return None, f"Error validating location: {str(e)}"

@app.route('/')
def index():
    """Main dashboard page"""
    return render_template('dashboard.html')

@app.route('/simple')
def simple_weather():
    """Simple weather page (original)"""
    return render_template('index.html')

@app.route('/weather', methods=['POST'])
def get_weather():
    data = request.get_json()
    location = data.get('location', '').strip()

    if not location:
        return jsonify({'error': 'Please enter a location'}), 400

    # Fetch current weather
    current_data = get_weather_data(location)
    if not current_data:
        return jsonify({'error': 'Could not find weather data for this location'}), 404

    # Fetch forecast
    forecast_data = get_forecast_data(location)

    # Process the data
    current_weather = format_weather_data(current_data)
    forecast = format_forecast_data(forecast_data)

    response = {
        'current': current_weather,
        'forecast': forecast
    }

    return jsonify(response)

def get_weather_data_for_date_range(location_data, start_date, end_date):
    """Get weather data for a date range"""
    try:
        lat = location_data['latitude']
        lon = location_data['longitude']

        # For this demo, we'll use current weather and forecast
        # In a real app, you'd use historical weather API for past dates
        weather_data = []

        # Get current weather
        current_url = f"{BASE_URL}/weather?lat={lat}&lon={lon}&appid={API_KEY}&units=metric"
        current_response = requests.get(current_url)

        if current_response.status_code == 200:
            current_data = current_response.json()
            weather_data.append({
                'date': date.today().isoformat(),
                'type': 'current',
                'temperature': current_data['main']['temp'],
                'feels_like': current_data['main']['feels_like'],
                'humidity': current_data['main']['humidity'],
                'pressure': current_data['main']['pressure'],
                'description': current_data['weather'][0]['description'],
                'icon': current_data['weather'][0]['icon'],
                'wind_speed': current_data['wind']['speed']
            })

        # Get forecast data
        forecast_url = f"{BASE_URL}/forecast?lat={lat}&lon={lon}&appid={API_KEY}&units=metric"
        forecast_response = requests.get(forecast_url)

        if forecast_response.status_code == 200:
            forecast_data = forecast_response.json()
            for item in forecast_data['list'][:10]:  # Limit to 10 forecast items
                dt = datetime.fromtimestamp(item['dt'])
                weather_data.append({
                    'date': dt.date().isoformat(),
                    'time': dt.time().isoformat(),
                    'type': 'forecast',
                    'temperature': item['main']['temp'],
                    'feels_like': item['main']['feels_like'],
                    'humidity': item['main']['humidity'],
                    'pressure': item['main']['pressure'],
                    'description': item['weather'][0]['description'],
                    'icon': item['weather'][0]['icon'],
                    'wind_speed': item['wind']['speed']
                })

        return weather_data, None

    except Exception as e:
        return None, f"Error fetching weather data: {str(e)}"

# CRUD Operations for Weather Requests

@app.route('/api/weather-requests', methods=['POST'])
def create_weather_request():
    """CREATE - Add new weather request"""
    try:
        data = request.get_json()

        # Validate required fields
        location = data.get('location', '').strip()
        start_date_str = data.get('start_date', '').strip()
        end_date_str = data.get('end_date', '').strip()
        user_notes = data.get('user_notes', '').strip()

        if not all([location, start_date_str, end_date_str]):
            return jsonify({'error': 'Location, start date, and end date are required'}), 400

        # Validate date range
        start_date, end_date, date_error = validate_date_range(start_date_str, end_date_str)
        if date_error:
            return jsonify({'error': date_error}), 400

        # Validate and normalize location
        location_data, location_error = validate_and_normalize_location(location)
        if location_error:
            return jsonify({'error': location_error}), 400

        # Get weather data
        weather_data, weather_error = get_weather_data_for_date_range(location_data, start_date, end_date)
        if weather_error:
            return jsonify({'error': weather_error}), 500

        # Create weather request record
        weather_request = WeatherRequest(
            location=location,
            normalized_location=location_data['normalized_name'],
            latitude=location_data['latitude'],
            longitude=location_data['longitude'],
            start_date=start_date,
            end_date=end_date,
            user_notes=user_notes
        )
        weather_request.set_weather_data(weather_data)

        db.session.add(weather_request)
        db.session.commit()

        return jsonify({
            'message': 'Weather request created successfully',
            'id': weather_request.id,
            'data': weather_request.to_dict()
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'Internal server error: {str(e)}'}), 500

@app.route('/api/weather-requests', methods=['GET'])
def get_weather_requests():
    """READ - Get all weather requests"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)

        # Limit per_page to prevent abuse
        per_page = min(per_page, 100)

        weather_requests = WeatherRequest.query.filter_by(is_active=True)\
                                             .order_by(WeatherRequest.request_date.desc())\
                                             .paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'requests': [req.to_dict() for req in weather_requests.items],
            'total': weather_requests.total,
            'pages': weather_requests.pages,
            'current_page': page,
            'per_page': per_page
        }), 200

    except Exception as e:
        return jsonify({'error': f'Error fetching weather requests: {str(e)}'}), 500

@app.route('/api/weather-requests/<int:request_id>', methods=['GET'])
def get_weather_request(request_id):
    """READ - Get specific weather request"""
    try:
        weather_request = WeatherRequest.query.filter_by(id=request_id, is_active=True).first()
        if not weather_request:
            return jsonify({'error': 'Weather request not found'}), 404

        return jsonify(weather_request.to_dict()), 200

    except Exception as e:
        return jsonify({'error': f'Error fetching weather request: {str(e)}'}), 500

@app.route('/api/weather-requests/<int:request_id>', methods=['PUT'])
def update_weather_request(request_id):
    """UPDATE - Update existing weather request"""
    try:
        weather_request = WeatherRequest.query.filter_by(id=request_id, is_active=True).first()
        if not weather_request:
            return jsonify({'error': 'Weather request not found'}), 404

        data = request.get_json()

        # Fields that can be updated
        location = data.get('location', '').strip()
        start_date_str = data.get('start_date', '').strip()
        end_date_str = data.get('end_date', '').strip()
        user_notes = data.get('user_notes', '').strip()

        # Update user notes (always allowed)
        if 'user_notes' in data:
            weather_request.user_notes = user_notes

        # Update location and dates if provided
        if location or start_date_str or end_date_str:
            # Use existing values if not provided
            location = location or weather_request.location
            start_date_str = start_date_str or weather_request.start_date.isoformat()
            end_date_str = end_date_str or weather_request.end_date.isoformat()

            # Validate date range
            start_date, end_date, date_error = validate_date_range(start_date_str, end_date_str)
            if date_error:
                return jsonify({'error': date_error}), 400

            # Validate and normalize location
            location_data, location_error = validate_and_normalize_location(location)
            if location_error:
                return jsonify({'error': location_error}), 400

            # Get updated weather data
            weather_data, weather_error = get_weather_data_for_date_range(location_data, start_date, end_date)
            if weather_error:
                return jsonify({'error': weather_error}), 500

            # Update the record
            weather_request.location = location
            weather_request.normalized_location = location_data['normalized_name']
            weather_request.latitude = location_data['latitude']
            weather_request.longitude = location_data['longitude']
            weather_request.start_date = start_date
            weather_request.end_date = end_date
            weather_request.set_weather_data(weather_data)

        db.session.commit()

        return jsonify({
            'message': 'Weather request updated successfully',
            'data': weather_request.to_dict()
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'Error updating weather request: {str(e)}'}), 500

@app.route('/api/weather-requests/<int:request_id>', methods=['DELETE'])
def delete_weather_request(request_id):
    """DELETE - Delete weather request (soft delete)"""
    try:
        weather_request = WeatherRequest.query.filter_by(id=request_id, is_active=True).first()
        if not weather_request:
            return jsonify({'error': 'Weather request not found'}), 404

        # Soft delete - just mark as inactive
        weather_request.is_active = False
        db.session.commit()

        return jsonify({'message': 'Weather request deleted successfully'}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'Error deleting weather request: {str(e)}'}), 500

# Data Export Endpoints

@app.route('/api/export/<format_type>')
def export_data(format_type):
    """Export weather requests in various formats"""
    try:
        # Get all active weather requests
        weather_requests = WeatherRequest.query.filter_by(is_active=True).all()

        if not weather_requests:
            return jsonify({'error': 'No weather requests found to export'}), 404

        if format_type.lower() == 'json':
            data = export_to_json(weather_requests)
            return send_file(
                io.BytesIO(data.encode()),
                mimetype='application/json',
                as_attachment=True,
                download_name=f'weather_requests_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            )

        elif format_type.lower() == 'csv':
            data = export_to_csv(weather_requests)
            return send_file(
                io.BytesIO(data.encode()),
                mimetype='text/csv',
                as_attachment=True,
                download_name=f'weather_requests_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
            )

        elif format_type.lower() == 'xml':
            data = export_to_xml(weather_requests)
            return send_file(
                io.BytesIO(data.encode()),
                mimetype='application/xml',
                as_attachment=True,
                download_name=f'weather_requests_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xml'
            )

        elif format_type.lower() == 'pdf':
            data = export_to_pdf(weather_requests)
            return send_file(
                io.BytesIO(data),
                mimetype='application/pdf',
                as_attachment=True,
                download_name=f'weather_requests_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf'
            )

        elif format_type.lower() == 'markdown' or format_type.lower() == 'md':
            data = export_to_markdown(weather_requests)
            return send_file(
                io.BytesIO(data.encode()),
                mimetype='text/markdown',
                as_attachment=True,
                download_name=f'weather_requests_{datetime.now().strftime("%Y%m%d_%H%M%S")}.md'
            )

        else:
            return jsonify({'error': 'Unsupported export format. Supported formats: json, csv, xml, pdf, markdown'}), 400

    except Exception as e:
        return jsonify({'error': f'Error exporting data: {str(e)}'}), 500

# Optional API Integration Endpoints

@app.route('/api/location-info/<int:request_id>')
def get_location_info(request_id):
    """Get additional location information (YouTube videos, maps, etc.)"""
    try:
        weather_request = WeatherRequest.query.filter_by(id=request_id, is_active=True).first()
        if not weather_request:
            return jsonify({'error': 'Weather request not found'}), 404

        location_info = {
            'location': weather_request.normalized_location,
            'latitude': weather_request.latitude,
            'longitude': weather_request.longitude,
            'google_maps_url': f"https://www.google.com/maps?q={weather_request.latitude},{weather_request.longitude}",
            'youtube_search_url': f"https://www.youtube.com/results?search_query={weather_request.normalized_location.replace(' ', '+').replace(',', '')}+travel+guide"
        }

        # Add Google Maps embed URL if API key is available
        if config.GOOGLE_MAPS_API_KEY:
            location_info['maps_embed_url'] = f"https://www.google.com/maps/embed/v1/place?key={config.GOOGLE_MAPS_API_KEY}&q={weather_request.latitude},{weather_request.longitude}"

        return jsonify(location_info), 200

    except Exception as e:
        return jsonify({'error': f'Error fetching location info: {str(e)}'}), 500

if __name__ == '__main__':
    with app.app_context():
        db.create_all()  # Create database tables
    app.run(debug=config.DEBUG, port=config.PORT)


