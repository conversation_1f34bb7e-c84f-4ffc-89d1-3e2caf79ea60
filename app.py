from flask import Flask, render_template, request, jsonify
import requests
import os
from datetime import datetime
import json
import config

app = Flask(__name__)

# OpenWeatherMap API configuration
API_KEY = config.API_KEY
BASE_URL = config.BASE_URL

def get_weather_data(location):
    """Get current weather data for a location"""
    try:
        # Try to parse as coordinates first (lat,lon)
        if ',' in location:
            lat, lon = location.split(',')
            url = f"{BASE_URL}/weather?lat={lat.strip()}&lon={lon.strip()}&appid={API_KEY}&units=metric"
        else:
            # Treat as city name or zip code
            url = f"{BASE_URL}/weather?q={location}&appid={API_KEY}&units=metric"

        response = requests.get(url)
        if response.status_code == 200:
            return response.json()
        else:
            return None
    except Exception as e:
        print(f"Error fetching weather data: {e}")
        return None

def get_forecast_data(location):
    """Get 5-day forecast data for a location"""
    try:
        # Try to parse as coordinates first (lat,lon)
        if ',' in location:
            lat, lon = location.split(',')
            url = f"{BASE_URL}/forecast?lat={lat.strip()}&lon={lon.strip()}&appid={API_KEY}&units=metric"
        else:
            # Treat as city name or zip code
            url = f"{BASE_URL}/forecast?q={location}&appid={API_KEY}&units=metric"

        response = requests.get(url)
        if response.status_code == 200:
            return response.json()
        else:
            return None
    except Exception as e:
        print(f"Error fetching forecast data: {e}")
        return None

def format_weather_data(weather_data):
    """Format weather data for display"""
    if not weather_data:
        return None

    return {
        'location': f"{weather_data['name']}, {weather_data['sys']['country']}",
        'temperature': round(weather_data['main']['temp']),
        'feels_like': round(weather_data['main']['feels_like']),
        'description': weather_data['weather'][0]['description'].title(),
        'icon': weather_data['weather'][0]['icon'],
        'humidity': weather_data['main']['humidity'],
        'pressure': weather_data['main']['pressure'],
        'wind_speed': weather_data['wind']['speed'],
        'wind_direction': weather_data['wind'].get('deg', 0),
        'visibility': weather_data.get('visibility', 0) / 1000,  # Convert to km
        'sunrise': datetime.fromtimestamp(weather_data['sys']['sunrise']).strftime('%H:%M'),
        'sunset': datetime.fromtimestamp(weather_data['sys']['sunset']).strftime('%H:%M'),
    }

def format_forecast_data(forecast_data):
    """Format 5-day forecast data for display"""
    if not forecast_data:
        return None

    daily_forecasts = {}

    for item in forecast_data['list']:
        date = datetime.fromtimestamp(item['dt']).strftime('%Y-%m-%d')

        if date not in daily_forecasts:
            daily_forecasts[date] = {
                'date': datetime.fromtimestamp(item['dt']).strftime('%A, %B %d'),
                'temps': [],
                'descriptions': [],
                'icons': [],
                'humidity': [],
                'wind_speed': []
            }

        daily_forecasts[date]['temps'].append(item['main']['temp'])
        daily_forecasts[date]['descriptions'].append(item['weather'][0]['description'])
        daily_forecasts[date]['icons'].append(item['weather'][0]['icon'])
        daily_forecasts[date]['humidity'].append(item['main']['humidity'])
        daily_forecasts[date]['wind_speed'].append(item['wind']['speed'])

    # Process daily data
    formatted_forecast = []
    for date, data in list(daily_forecasts.items())[:5]:  # Only take 5 days
        formatted_forecast.append({
            'date': data['date'],
            'temp_max': round(max(data['temps'])),
            'temp_min': round(min(data['temps'])),
            'description': max(set(data['descriptions']), key=data['descriptions'].count).title(),
            'icon': max(set(data['icons']), key=data['icons'].count),
            'humidity': round(sum(data['humidity']) / len(data['humidity'])),
            'wind_speed': round(sum(data['wind_speed']) / len(data['wind_speed']), 1)
        })

    return formatted_forecast

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')

@app.route('/weather', methods=['POST'])
def get_weather():
    """API endpoint to get weather data"""
    data = request.get_json()
    location = data.get('location', '').strip()

    if not location:
        return jsonify({'error': 'Location is required'}), 400

    # Get current weather
    weather_data = get_weather_data(location)
    if not weather_data:
        return jsonify({'error': 'Location not found or API error'}), 404

    # Get forecast data
    forecast_data = get_forecast_data(location)

    # Format data
    current_weather = format_weather_data(weather_data)
    forecast = format_forecast_data(forecast_data)

    return jsonify({
        'current': current_weather,
        'forecast': forecast
    })

if __name__ == '__main__':
    app.run(debug=config.DEBUG, port=config.PORT)


