from flask import Flask, render_template, request, jsonify
import requests
import os
from datetime import datetime
import json
import config

app = Flask(__name__)

# TODO: move this to environment variables later
API_KEY = config.API_KEY
BASE_URL = config.BASE_URL

def get_weather_data(location):
    """Get current weather data for a location"""
    try:
        # Check if it's coordinates (lat,lon format)
        if ',' in location:
            coords = location.split(',')
            lat = coords[0].strip()
            lon = coords[1].strip()
            url = f"{BASE_URL}/weather?lat={lat}&lon={lon}&appid={API_KEY}&units=metric"
        else:
            # Regular location search
            url = f"{BASE_URL}/weather?q={location}&appid={API_KEY}&units=metric"

        resp = requests.get(url)
        if resp.status_code == 200:
            return resp.json()
        else:
            print(f"API returned status code: {resp.status_code}")
            return None
    except Exception as e:
        print(f"Error: {e}")
        return None

def get_forecast_data(location):
    """Get 5-day forecast data for a location"""
    try:
        # Same logic as weather data - maybe should refactor this later
        if ',' in location:
            coords = location.split(',')
            lat = coords[0].strip()
            lon = coords[1].strip()
            url = f"{BASE_URL}/forecast?lat={lat}&lon={lon}&appid={API_KEY}&units=metric"
        else:
            url = f"{BASE_URL}/forecast?q={location}&appid={API_KEY}&units=metric"

        response = requests.get(url)
        if response.status_code == 200:
            return response.json()
        else:
            return None
    except Exception as e:
        print(f"Error fetching forecast data: {e}")
        return None

def format_weather_data(weather_data):
    if not weather_data:
        return None

    # Extract main weather info
    main = weather_data['main']
    weather = weather_data['weather'][0]
    sys_data = weather_data['sys']

    formatted = {
        'location': f"{weather_data['name']}, {sys_data['country']}",
        'temperature': round(main['temp']),
        'feels_like': round(main['feels_like']),
        'description': weather['description'].title(),
        'icon': weather['icon'],
        'humidity': main['humidity'],
        'pressure': main['pressure'],
        'wind_speed': weather_data['wind']['speed'],
        'wind_direction': weather_data['wind'].get('deg', 0),
        'sunrise': datetime.fromtimestamp(sys_data['sunrise']).strftime('%H:%M'),
        'sunset': datetime.fromtimestamp(sys_data['sunset']).strftime('%H:%M'),
    }

    # Handle visibility (not always present)
    if 'visibility' in weather_data:
        formatted['visibility'] = weather_data['visibility'] / 1000
    else:
        formatted['visibility'] = 0

    return formatted

def format_forecast_data(forecast_data):
    if not forecast_data:
        return None

    daily_data = {}

    # Group forecast data by date
    for item in forecast_data['list']:
        dt = datetime.fromtimestamp(item['dt'])
        date_key = dt.strftime('%Y-%m-%d')

        if date_key not in daily_data:
            daily_data[date_key] = {
                'date': dt.strftime('%A, %B %d'),
                'temps': [],
                'descriptions': [],
                'icons': [],
                'humidity_vals': [],
                'wind_vals': []
            }

        daily_data[date_key]['temps'].append(item['main']['temp'])
        daily_data[date_key]['descriptions'].append(item['weather'][0]['description'])
        daily_data[date_key]['icons'].append(item['weather'][0]['icon'])
        daily_data[date_key]['humidity_vals'].append(item['main']['humidity'])
        daily_data[date_key]['wind_vals'].append(item['wind']['speed'])

    # Build final forecast list
    forecast_list = []
    count = 0
    for date_key, data in daily_data.items():
        if count >= 5:  # Only want 5 days
            break

        # Find most common description and icon
        desc_counts = {}
        for desc in data['descriptions']:
            desc_counts[desc] = desc_counts.get(desc, 0) + 1
        most_common_desc = max(desc_counts, key=desc_counts.get)

        icon_counts = {}
        for icon in data['icons']:
            icon_counts[icon] = icon_counts.get(icon, 0) + 1
        most_common_icon = max(icon_counts, key=icon_counts.get)

        forecast_item = {
            'date': data['date'],
            'temp_max': round(max(data['temps'])),
            'temp_min': round(min(data['temps'])),
            'description': most_common_desc.title(),
            'icon': most_common_icon,
            'humidity': round(sum(data['humidity_vals']) / len(data['humidity_vals'])),
            'wind_speed': round(sum(data['wind_vals']) / len(data['wind_vals']), 1)
        }

        forecast_list.append(forecast_item)
        count += 1

    return forecast_list

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')

@app.route('/weather', methods=['POST'])
def get_weather():
    data = request.get_json()
    location = data.get('location', '').strip()

    if not location:
        return jsonify({'error': 'Please enter a location'}), 400

    # Fetch current weather
    current_data = get_weather_data(location)
    if not current_data:
        return jsonify({'error': 'Could not find weather data for this location'}), 404

    # Fetch forecast
    forecast_data = get_forecast_data(location)

    # Process the data
    current_weather = format_weather_data(current_data)
    forecast = format_forecast_data(forecast_data)

    response = {
        'current': current_weather,
        'forecast': forecast
    }

    return jsonify(response)

if __name__ == '__main__':
    app.run(debug=config.DEBUG, port=config.PORT)


