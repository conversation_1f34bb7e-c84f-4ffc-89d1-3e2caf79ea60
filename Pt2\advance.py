from flask import Flask, render_template, request, jsonify, send_file, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
import requests
import os
from datetime import datetime, date, timedelta
import json
import csv
import io
from dateutil import parser
from difflib import get_close_matches
import xml.etree.ElementTree as ET
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib import colors

app = Flask(__name__)

# Configuration
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///weather_app.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SECRET_KEY'] = 'dev-secret-key-change-in-production'

# Initialize database
db = SQLAlchemy(app)

# API Configuration
OPENWEATHER_API_KEY = '********************************'
OPENWEATHER_BASE_URL = "http://api.openweathermap.org/data/2.5"
YOUTUBE_API_KEY = ''  # Optional - add your YouTube API key
GOOGLE_MAPS_API_KEY = ''  # Optional - add your Google Maps API key

# Database Models
class WeatherRequest(db.Model):
    __tablename__ = 'weather_requests'

    id = db.Column(db.Integer, primary_key=True)
    location = db.Column(db.String(200), nullable=False)
    normalized_location = db.Column(db.String(200), nullable=False)
    latitude = db.Column(db.Float, nullable=True)
    longitude = db.Column(db.Float, nullable=True)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    request_date = db.Column(db.DateTime, default=datetime.utcnow)
    weather_data = db.Column(db.Text, nullable=True)  # JSON string
    user_notes = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.Boolean, default=True)

    def set_weather_data(self, data):
        if data:
            self.weather_data = json.dumps(data)

    def get_weather_data(self):
        if self.weather_data:
            try:
                return json.loads(self.weather_data)
            except json.JSONDecodeError:
                return None
        return None

    def to_dict(self):
        return {
            'id': self.id,
            'location': self.location,
            'normalized_location': self.normalized_location,
            'latitude': self.latitude,
            'longitude': self.longitude,
            'start_date': self.start_date.isoformat() if self.start_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'request_date': self.request_date.isoformat() if self.request_date else None,
            'weather_data': self.get_weather_data(),
            'user_notes': self.user_notes,
            'is_active': self.is_active
        }

class LocationCache(db.Model):
    __tablename__ = 'location_cache'

    id = db.Column(db.Integer, primary_key=True)
    search_term = db.Column(db.String(200), unique=True, nullable=False)
    normalized_name = db.Column(db.String(200), nullable=False)
    latitude = db.Column(db.Float, nullable=False)
    longitude = db.Column(db.Float, nullable=False)
    country = db.Column(db.String(100), nullable=True)
    state = db.Column(db.String(100), nullable=True)
    created_date = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'search_term': self.search_term,
            'normalized_name': self.normalized_name,
            'latitude': self.latitude,
            'longitude': self.longitude,
            'country': self.country,
            'state': self.state
        }

# Utility Functions
def validate_date_range(start_date_str, end_date_str):
    """Validate and parse date range"""
    try:
        start_date = parser.parse(start_date_str).date()
        end_date = parser.parse(end_date_str).date()

        # Check if dates are valid
        if start_date > end_date:
            return None, None, "Start date cannot be after end date"

        # Check if date range is not too far in the future
        max_future_date = date.today() + timedelta(days=5)
        if start_date > max_future_date:
            return None, None, "Start date cannot be more than 5 days in the future"

        # Check if date range is not too far in the past (OpenWeatherMap limitation)
        min_past_date = date.today() - timedelta(days=5)
        if end_date < min_past_date:
            return None, None, "End date cannot be more than 5 days in the past"

        return start_date, end_date, None
    except (ValueError, TypeError) as e:
        return None, None, f"Invalid date format: {str(e)}"

def validate_and_normalize_location(location):
    """Validate location and return normalized data with fuzzy matching"""
    if not location or len(location.strip()) < 2:
        return None, "Location must be at least 2 characters long"

    location = location.strip()

    # Check cache first
    cached = LocationCache.query.filter_by(search_term=location.lower()).first()
    if cached:
        return cached.to_dict(), None

    # Try to get location data from OpenWeatherMap
    try:
        # Check if it's coordinates
        if ',' in location:
            coords = location.split(',')
            if len(coords) == 2:
                try:
                    lat = float(coords[0].strip())
                    lon = float(coords[1].strip())
                    if -90 <= lat <= 90 and -180 <= lon <= 180:
                        # Reverse geocoding to get location name
                        url = f"{OPENWEATHER_BASE_URL}/weather?lat={lat}&lon={lon}&appid={OPENWEATHER_API_KEY}"
                        response = requests.get(url)
                        if response.status_code == 200:
                            data = response.json()
                            normalized_name = f"{data['name']}, {data['sys']['country']}"

                            # Cache the result
                            cache_entry = LocationCache(
                                search_term=location.lower(),
                                normalized_name=normalized_name,
                                latitude=lat,
                                longitude=lon,
                                country=data['sys']['country']
                            )
                            db.session.add(cache_entry)
                            db.session.commit()

                            return cache_entry.to_dict(), None
                except ValueError:
                    pass

        # Regular location search
        url = f"{OPENWEATHER_BASE_URL}/weather?q={location}&appid={OPENWEATHER_API_KEY}"
        response = requests.get(url)

        if response.status_code == 200:
            data = response.json()
            normalized_name = f"{data['name']}, {data['sys']['country']}"

            # Cache the result
            cache_entry = LocationCache(
                search_term=location.lower(),
                normalized_name=normalized_name,
                latitude=data['coord']['lat'],
                longitude=data['coord']['lon'],
                country=data['sys']['country']
            )
            db.session.add(cache_entry)
            db.session.commit()

            return cache_entry.to_dict(), None

        elif response.status_code == 404:
            # Try fuzzy matching with cached locations
            all_locations = LocationCache.query.all()
            location_names = [loc.search_term for loc in all_locations]
            matches = get_close_matches(location.lower(), location_names, n=3, cutoff=0.6)

            if matches:
                suggestions = []
                for match in matches:
                    cached_loc = LocationCache.query.filter_by(search_term=match).first()
                    if cached_loc:
                        suggestions.append(cached_loc.normalized_name)

                return None, f"Location not found. Did you mean: {', '.join(suggestions)}?"

            return None, "Location not found. Please check spelling or try a different location."

        else:
            return None, f"Error validating location: API returned status {response.status_code}"

    except Exception as e:
        return None, f"Error validating location: {str(e)}"

def get_weather_data_for_date_range(location_data, start_date, end_date):
    """Get weather data for a date range"""
    try:
        lat = location_data['latitude']
        lon = location_data['longitude']

        # For this demo, we'll use current weather and forecast
        # In a real app, you'd use historical weather API for past dates
        weather_data = []

        # Get current weather
        current_url = f"{OPENWEATHER_BASE_URL}/weather?lat={lat}&lon={lon}&appid={OPENWEATHER_API_KEY}&units=metric"
        current_response = requests.get(current_url)

        if current_response.status_code == 200:
            current_data = current_response.json()
            weather_data.append({
                'date': date.today().isoformat(),
                'type': 'current',
                'temperature': current_data['main']['temp'],
                'feels_like': current_data['main']['feels_like'],
                'humidity': current_data['main']['humidity'],
                'pressure': current_data['main']['pressure'],
                'description': current_data['weather'][0]['description'],
                'icon': current_data['weather'][0]['icon'],
                'wind_speed': current_data['wind']['speed']
            })

        # Get forecast data
        forecast_url = f"{OPENWEATHER_BASE_URL}/forecast?lat={lat}&lon={lon}&appid={OPENWEATHER_API_KEY}&units=metric"
        forecast_response = requests.get(forecast_url)

        if forecast_response.status_code == 200:
            forecast_data = forecast_response.json()
            for item in forecast_data['list'][:10]:  # Limit to 10 forecast items
                dt = datetime.fromtimestamp(item['dt'])
                weather_data.append({
                    'date': dt.date().isoformat(),
                    'time': dt.time().isoformat(),
                    'type': 'forecast',
                    'temperature': item['main']['temp'],
                    'feels_like': item['main']['feels_like'],
                    'humidity': item['main']['humidity'],
                    'pressure': item['main']['pressure'],
                    'description': item['weather'][0]['description'],
                    'icon': item['weather'][0]['icon'],
                    'wind_speed': item['wind']['speed']
                })

        return weather_data, None

    except Exception as e:
        return None, f"Error fetching weather data: {str(e)}"

# Routes
@app.route('/')
def index():
    """Main dashboard page"""
    return render_template('dashboard.html')

@app.route('/api/weather-requests', methods=['POST'])
def create_weather_request():
    """CREATE - Add new weather request"""
    try:
        data = request.get_json()

        # Validate required fields
        location = data.get('location', '').strip()
        start_date_str = data.get('start_date', '').strip()
        end_date_str = data.get('end_date', '').strip()
        user_notes = data.get('user_notes', '').strip()

        if not all([location, start_date_str, end_date_str]):
            return jsonify({'error': 'Location, start date, and end date are required'}), 400

        # Validate date range
        start_date, end_date, date_error = validate_date_range(start_date_str, end_date_str)
        if date_error:
            return jsonify({'error': date_error}), 400

        # Validate and normalize location
        location_data, location_error = validate_and_normalize_location(location)
        if location_error:
            return jsonify({'error': location_error}), 400

        # Get weather data
        weather_data, weather_error = get_weather_data_for_date_range(location_data, start_date, end_date)
        if weather_error:
            return jsonify({'error': weather_error}), 500

        # Create weather request record
        weather_request = WeatherRequest(
            location=location,
            normalized_location=location_data['normalized_name'],
            latitude=location_data['latitude'],
            longitude=location_data['longitude'],
            start_date=start_date,
            end_date=end_date,
            user_notes=user_notes
        )
        weather_request.set_weather_data(weather_data)

        db.session.add(weather_request)
        db.session.commit()

        return jsonify({
            'message': 'Weather request created successfully',
            'id': weather_request.id,
            'data': weather_request.to_dict()
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'Internal server error: {str(e)}'}), 500

@app.route('/api/weather-requests', methods=['GET'])
def get_weather_requests():
    """READ - Get all weather requests"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)

        # Limit per_page to prevent abuse
        per_page = min(per_page, 100)

        weather_requests = WeatherRequest.query.filter_by(is_active=True)\
                                             .order_by(WeatherRequest.request_date.desc())\
                                             .paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'requests': [req.to_dict() for req in weather_requests.items],
            'total': weather_requests.total,
            'pages': weather_requests.pages,
            'current_page': page,
            'per_page': per_page
        }), 200

    except Exception as e:
        return jsonify({'error': f'Error fetching weather requests: {str(e)}'}), 500

@app.route('/api/weather-requests/<int:request_id>', methods=['GET'])
def get_weather_request(request_id):
    """READ - Get specific weather request"""
    try:
        weather_request = WeatherRequest.query.filter_by(id=request_id, is_active=True).first()
        if not weather_request:
            return jsonify({'error': 'Weather request not found'}), 404

        return jsonify(weather_request.to_dict()), 200

    except Exception as e:
        return jsonify({'error': f'Error fetching weather request: {str(e)}'}), 500

@app.route('/api/weather-requests/<int:request_id>', methods=['PUT'])
def update_weather_request(request_id):
    """UPDATE - Update existing weather request"""
    try:
        weather_request = WeatherRequest.query.filter_by(id=request_id, is_active=True).first()
        if not weather_request:
            return jsonify({'error': 'Weather request not found'}), 404

        data = request.get_json()

        # Fields that can be updated
        location = data.get('location', '').strip()
        start_date_str = data.get('start_date', '').strip()
        end_date_str = data.get('end_date', '').strip()
        user_notes = data.get('user_notes', '').strip()

        # Update user notes (always allowed)
        if 'user_notes' in data:
            weather_request.user_notes = user_notes

        # Update location and dates if provided
        if location or start_date_str or end_date_str:
            # Use existing values if not provided
            location = location or weather_request.location
            start_date_str = start_date_str or weather_request.start_date.isoformat()
            end_date_str = end_date_str or weather_request.end_date.isoformat()

            # Validate date range
            start_date, end_date, date_error = validate_date_range(start_date_str, end_date_str)
            if date_error:
                return jsonify({'error': date_error}), 400

            # Validate and normalize location
            location_data, location_error = validate_and_normalize_location(location)
            if location_error:
                return jsonify({'error': location_error}), 400

            # Get updated weather data
            weather_data, weather_error = get_weather_data_for_date_range(location_data, start_date, end_date)
            if weather_error:
                return jsonify({'error': weather_error}), 500

            # Update the record
            weather_request.location = location
            weather_request.normalized_location = location_data['normalized_name']
            weather_request.latitude = location_data['latitude']
            weather_request.longitude = location_data['longitude']
            weather_request.start_date = start_date
            weather_request.end_date = end_date
            weather_request.set_weather_data(weather_data)

        db.session.commit()

        return jsonify({
            'message': 'Weather request updated successfully',
            'data': weather_request.to_dict()
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'Error updating weather request: {str(e)}'}), 500

@app.route('/api/weather-requests/<int:request_id>', methods=['DELETE'])
def delete_weather_request(request_id):
    """DELETE - Delete weather request (soft delete)"""
    try:
        weather_request = WeatherRequest.query.filter_by(id=request_id, is_active=True).first()
        if not weather_request:
            return jsonify({'error': 'Weather request not found'}), 404

        # Soft delete - just mark as inactive
        weather_request.is_active = False
        db.session.commit()

        return jsonify({'message': 'Weather request deleted successfully'}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'Error deleting weather request: {str(e)}'}), 500